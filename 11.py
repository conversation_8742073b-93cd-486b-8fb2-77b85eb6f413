import base64
import gzip
import json

# 您提供的 parsed_content 数据
parsed_content = {
    b'crc': [b'-1004783064'],
    b'gzip': [b'9'],
    b'data_list': [
        b'[{"ekey":"Rz7kftbxZtbi4TVgRTHi5dNBzwPEKIsw0QY6B4FY3wLBWWcN9txhC5QpmRJYNTOC7BVm\\/WL0gOJD1hNzs+NmCeV8lTL+KTBzDdYqpPjLH16Kuj6s7V7a6d2TkwSBz1GlTnmz\\/BadF5VkxIEnofGgQzRhvi\\/Obi+UFfKx9zd2OCNXUhG\\/7sPowbwg+f1sRBeyMLRhb4acwetUxfTJYCSPLnLyzY9fKK6bUvQtG9ma2xosMhkCdAQJ+GfGlYvrM4WyrIdvb43Bkfzof9y7\\/wKE0oTK8vtufr7sZAUlGwjdjFMt3BrDWuWXSSHue0bLiZXJGdJsfgW9LLcCbPaleVVvtQ==","payloads":["HVc75eWJaWoY4FlOiHncc7m3WyT6RP+jXkWZd0DNIbRHO5loRvrv5iJjWlklssHaqKzIYOznqzy699fLYE8tz5Tll2f2qIvwx+GZbDOrK4xVe1mj7fQqofb5crV96towfYlYZRpGSFpmGDYVjfWyFD6UjgQdCpR9+qkCp79lsuWAa3ZzIcKLyTE\\/eQkZySZQDRGBRoBMGFMzRA1Qm3VBwwIkWBSoupBg2U6PS4a4QqYLiVIlbOyPNt015bRKYAagpMNRkKosAvKxIE73otN80S3eSRw3quHhbuNknz4uUuFOOImJENQ20cBNSaskBktQRdLY0PRJZ+hGV6kF\\/FrmEG2WfmmLunDb2uu\\/IwMXoo1EwPBS0RDP\\/Kfv+yxutybozAdrVZmzybYzL7prsOtXvSR1vF2C6vHiA25Is7yUEVKXaBoOaUjGtMoysUN3TlaS2nbVa0YLfp8E\\/Px1GMb\\/RMpwZzO8X8RImbV8lxMBasjOeiO8+L+aHDZsUuAM0iF81V\\/uLUzCgB9q9YNnncDfYdBsWh3vTNs\\/LYsne3NslRwLfkV0N\\/fcRbAA1fQgWWztlCafiumewz8OTLygP1w3x3nvOm0mwl+pE9o5saFEjTzbKSlAWyDu8XdyxZfGnwVOG5fxMvaMr1VtMrdFEw0b12iL9P5mfkCWu40k33eD+spJsJX8fPBMGQVTgUu9\\/qiNJH0d7xH\\/5onME2qAxi7a1CHY+w3EMvvpoQ4OiEbvspf4VLjQCtVQGDwt9U9bKfQEScR2a4aaeXvRQGlnHP\\/IK+9O+nxePsLynQ5wUzg\\/ygbnB2cXJJXvYk1K0PCohkLRxzDJCMCD5TGn5BcGnotcI6G0yt0d1wHZf7UFq8gDh2gzGPGvTBj48bBkZotbJRzhcaHw7zCA5jemQ1RbD6x\\/X\\/2Nx+pIMisIGeHa\\/qR2hWsOFFOsoYovyiEQhJQXj\\/iz\\/xZNAFKfbcXro4\\/qA+4jcd\\/ZdgJRzUk7YDYH98+Li4aM5\\/5+RL1BTc4807z\\/5iAmXGlNlFUUx+eZn94nq0mO0g==","jNcL+tctVLNyGs5hX25WayBUzlXfzD2bVzPRRihEi5j0eQJyYu+vgMjrefiinE4Nbc5UcBFgh3xJ+fsV2oMAfcvvaXWBPCzBb1EowFdjFJvi9OtxwY\\/tQ60lDfjvdDpx6seZ6ZMU3jxQ1Q6vGvdwV2DrbafwbjMTBm9VojLlNWocMM+h9DJXGFaVu7YNCM+9eyK+tLK0SrKrcHZUYx+hneQFFcY1TSK7KWBrV8ec+D5QhwWc3HRxpoliXngZueEfTUXxPvS1gsIJ5s6NoAg6mNfr1OhMxVUnQbm0SNRFGSg\\/XUdBCRt\\/OIACmyy6KPCTWsvlD3WN2nCuuLIl39gKpMd6eM+shFSyQ\\/f67DkxBRqNuPeczCbttlq2GxTfmui3DOE+mkC3x6qeMCwf1FuUZbIARPnAB1jG2Te1L5UmcawSl\\/utlHjMAbt0DLYpVWWM\\/qSd+seV2Fc87uqvypcMWgdikoUrhWud+rjQWO6eiobcZch8goKy+HwkQAs7QpO48u5mW8UP4bRBzC9\\/R3krU65thfZOSjXGnQwIaZUropH+eUVurgJMa9fFt4G4Hlqlkwg2te\\/4uDKp3b6HR1mTgSKHiLPbY++fNCj2bwxnI31LCg2A2BJj+Nx64zv3x2wWJSq9XuT6ZeCNPzTJrCpIYtXA+5KyhsfzB4PiZW+uD0hakf1nGDNiTymLVA3M0JUjX\\/fIl4TOAV\\/Uw6B95ZwZVLDY7nSdxVEv61sad0J\\/gUTiqza1NBsn5YwpZMSb37LRxBsyi7UWOpP2GsqRCV3VQs+ZULhDCVe0cTZo5mSW2utp0+IrQnvmdWbouioY+4x6GPz8WSWjv9KV5S1c3HrtYJGCRqxvGPprk9OnwYjHR+AtIVi4WGcKxChol6YNpheUFXknahyLmUmML6NgMPfeh430LFb22DFXiwUt5zSsc5UvVmx5PKfHSghItm323cqQFv7\\/S5yA5Zi+cxBHZRdG930pI92I7p73YZbKLyDB+kITazN9A1ZEwWoRsNnRt5z124JFu7vAuUnjT67YFT+Jiz3mkAss4LfFK6X80sHLfEwQdLGmjHPCQ5bzKlVZVjVBUief96mUa9s+eC8tpoFCQsMgy37+3EjSlNQVbndlqW+D7aZZzxp6egw42aX2G2IV","b+yqMp+UHg9WqFGStgIdMrYdyxxC024Eioi6VJImREneM\\/U0Tmjm6rjaE27p4X6nCSybD1DQpji32JQC3OnrCCpuYXszTIOVLhKFyFfh4SsicZAwpAbz81b73d0yRDuS8agwMxW7qJR+cZyacqpwHOQy68Tiw64J0mxzrLyl1Ka5rDRmOWtw2efGLL76X2pVRyY3fTRSrE3YrfgzfP64wP4qqIGjns6DM32FrnlJ0y+DljFFnSHAnqy1cDb+NWCr\\/odmBn7pKdmirKJv6hYquEBxfU4RbxhEq575UgEqlsGNS++4LxzWcNX\\/EXoMAlnIp49mPsVI7hguwbHXx20\\/VUJUNVIKh0qQXzlJRUw1Hd6i5zrJFb8cu1kdLV9BzZyRCTVovGwU2C8E92RKqk196lYSirdWqEoI4sQREo5UrkUG5ci1uBKAid5pesLIMqv1kTESSBMeyNRj5\\/xzY1lIwERA2gKEdxwioNXNSBeiOvUhig\\/bcwNgKn4H1qD7q9i7c4aNUrjLR1PMWnNBKNYPbsS8kUo23UD746t17Zlc2Z6DjI3uZiZxrl7oDG\\/Ynr7ag+JVHK3Tz75F8fQeki3D1olSNBM39TlVHxOiQumFx5SLVX0tNVwh3jvZKuThGuotICJVlOSzW+dSt8xJJE9k8KPV4llgkyTe3jOVD1Fq\\/2U0\\/GvqXn76lBUcD8wNS0o4ocRm4Cfb1QirEur9nxsIb+XBRCCuZSAE2bH+JFzCB5yTBc8I\\/aHLecQCeI5jy7Ijqw54nMRO2fnZGMzc9Oq8XaBP+L70tturlkFeh8CBiZHfmwlwWoZQs4ePz0bBJlka91wzcY3Z22Uzg0su7ShOmC9tAW6Li+uZ+nW7WqoVSAaykJCcgUjaL9XtgLMbjDibAwTQPjL28vQzGFjS7HN\\/BsK3dfswhdcCra0zob195BCE9ruvUynOLj6ugjDLl4RfqbV+Xs8Ix4HInReDSRAlCBy4OE4tZrZzdbW9rtTINOE="],"pkv":1,"flush_time":1756267350326}]']
}

# 解码 data_list 中的字节字符串并解析 JSON
data_list_str = parsed_content[b'data_list'][0].decode('utf-8')
data_list = json.loads(data_list_str)

# 提取 payloads
payloads = data_list[0]['payloads']

# 处理每个 payload
for i, payload in enumerate(payloads):
    print(f"处理 payload {i + 1}:")
    try:
        # Base64 解码
        decoded_data = base64.b64decode(payload)
        print(f"  Base64 解码后长度: {len(decoded_data)} 字节")

        # 尝试 gzip 解压
        decompressed_data = gzip.decompress(decoded_data)
        print(f"  Gzip 解压成功: {len(decompressed_data)} 字节")
        print(f"  解压后数据（前100字节）: {decompressed_data[:100]}")

        # 尝试将解压后的数据解码为 UTF-8 字符串（假设是文本数据）
        try:
            decompressed_text = decompressed_data.decode('utf-8')
            print(f"  解压文本（前100字符）: {decompressed_text[:100]}...")
        except UnicodeDecodeError:
            print("  解压数据不是 UTF-8 文本，可能是二进制数据。")
    except Exception as e:
        print(f"  处理失败: {e}")
    print()