from garbevents.sensors_events import GetData
from garbevents.settings import Settings as ST

# 埋点上传url
# ST.url = 'https://www.data-hw-log-sa.qu-in.top/sa?project=default'
ST.url = 'https://www.data-hw-log-sa.qu-in.top/sa?project=default'
# 报告生成路径
ST.report_path = 'report'
# 所有事件名称
ST.all_events = ['VipView', 'VipPackageClick', 'PayResult', 'AppElementClick', 'AppElementDisplay', 'PrintResult', '$AppViewScreen', '$AppStart']

addons = [
    GetData()
]